import React, { useState } from 'react';

const BookingAppointment = () => {
  const [selectedDate, setSelectedDate] = useState(15);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState('10:30 AM');
  const [consultationMode, setConsultationMode] = useState('In-person');
  const [paymentMethod, setPaymentMethod] = useState('Card');
  const [formData, setFormData] = useState({
    fullName: 'John Do<PERSON>',
    age: '',
    gender: 'Male',
    phoneNumber: '983564',
    email: '',
    address: '',
    reason: ''
  });

  const timeSlots = {
    Morning: ['9:00 AM', '10:30 AM'],
    Afternoon: ['2:00 PM', '3:30 PM', '5:00 PM'],
    Evening: ['6:30 PM', '8:00 PM']
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateCalendar = () => {
    const days = [];
    const daysInMonth = 30;
    const startDay = 3; // June starts on Thursday (3)
    
    // Add empty cells for days before the month starts
    for (let i = 0; i < startDay; i++) {
      days.push(<div key={`empty-${i}`} className="p-2"></div>);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const isSelected = day === selectedDate;
      const isAvailable = [15, 16, 19, 20].includes(day);
      
      days.push(
        <div
          key={day}
          onClick={() => isAvailable && setSelectedDate(day)}
          className={`p-2 text-center cursor-pointer rounded ${
            isSelected
              ? 'bg-blue-600 text-white'
              : isAvailable
              ? 'bg-gray-100 hover:bg-gray-200 text-gray-900'
              : 'text-gray-300 cursor-not-allowed'
          }`}
        >
          {day}
        </div>
      );
    }
    
    return days;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Book Appointment</h1>
          
          {/* Progress Steps */}
          <div className="flex items-center justify-center sm:justify-start space-x-4 sm:space-x-8 mb-8">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm">
                ✓
              </div>
              <span className="ml-2 text-sm font-medium text-green-600 hidden sm:inline">Select Doctor</span>
            </div>
            
            <div className="w-8 sm:w-12 h-0.5 bg-blue-600"></div>
            
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm">
                2
              </div>
              <span className="ml-2 text-sm font-medium text-blue-600 hidden sm:inline">Book Appointment</span>
            </div>
            
            <div className="w-8 sm:w-12 h-0.5 bg-gray-300"></div>
            
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-sm">
                3
              </div>
              <span className="ml-2 text-sm font-medium text-gray-600 hidden sm:inline">Payment</span>
            </div>
            
            <div className="w-8 sm:w-12 h-0.5 bg-gray-300"></div>
            
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-sm">
                4
              </div>
              <span className="ml-2 text-sm font-medium text-gray-600 hidden sm:inline">Confirmation</span>
            </div>
          </div>

          {/* Doctor Info */}
          <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 mb-6">
            <div className="flex items-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                <span className="text-xl sm:text-2xl">👨‍⚕️</span>
              </div>
              <div className="flex-1">
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Dr. Ayush Sharma</h2>
                <p className="text-blue-600 text-sm sm:text-base">Cardiologist</p>
                <p className="text-gray-600 text-sm">🏥 City General Hospital</p>
                <div className="flex flex-wrap items-center gap-4 mt-2 text-sm">
                  <span className="text-gray-600">📅 Wed, 15 Jun 2023 | 10:30 AM</span>
                  <span className="text-gray-600">💰 Consultation Fee: ₹800</span>
                </div>
                <button className="text-blue-600 text-sm mt-1 hover:underline">📝 Edit appointment time</button>
              </div>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Left Column - Forms */}
          <div className="lg:col-span-2 space-y-6">
            {/* Patient Details */}
            <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
              <div className="flex items-center mb-4">
                <span className="text-blue-600 mr-2">👤</span>
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Patient Details</h2>
              </div>
              
              <div className="grid sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.fullName}
                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter full name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Age <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    value={formData.age}
                    onChange={(e) => handleInputChange('age', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter age"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Gender <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter phone number"
                  />
                  <p className="text-xs text-gray-500 mt-1">Please enter a valid phone number</p>
                </div>
                
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter email address"
                  />
                </div>
                
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address (Optional)
                  </label>
                  <input
                    type="text"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your address"
                  />
                </div>
                
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reason for Visit / Symptoms <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={formData.reason}
                    onChange={(e) => handleInputChange('reason', e.target.value)}
                    rows={4}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Please describe your symptoms or reason for consultation"
                  />
                </div>
              </div>
            </div>

            {/* Appointment Preferences */}
            <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
              <div className="flex items-center mb-4">
                <span className="text-blue-600 mr-2">📅</span>
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Appointment Preferences</h2>
              </div>
              
              {/* Calendar */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">June 2023</h3>
                  <div className="flex space-x-2">
                    <button className="p-1 hover:bg-gray-100 rounded">‹</button>
                    <button className="p-1 hover:bg-gray-100 rounded">›</button>
                  </div>
                </div>
                
                <div className="grid grid-cols-7 gap-1 mb-2">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                    <div key={day} className="p-2 text-center text-sm font-medium text-gray-600">
                      {day}
                    </div>
                  ))}
                </div>
                
                <div className="grid grid-cols-7 gap-1">
                  {generateCalendar()}
                </div>
              </div>

              {/* Time Slots */}
              <div className="mb-6">
                <h3 className="text-sm font-semibold text-gray-900 mb-3">Select Time Slot</h3>
                <div className="space-y-4">
                  {Object.entries(timeSlots).map(([period, slots]) => (
                    <div key={period}>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">{period}</h4>
                      <div className="flex flex-wrap gap-2">
                        {slots.map((slot) => (
                          <button
                            key={slot}
                            onClick={() => setSelectedTimeSlot(slot)}
                            className={`px-4 py-2 text-sm rounded-lg border ${
                              selectedTimeSlot === slot
                                ? 'bg-blue-600 text-white border-blue-600'
                                : 'bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100'
                            }`}
                          >
                            {slot}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Consultation Mode */}
              <div>
                <h3 className="text-sm font-semibold text-gray-900 mb-3">
                  Mode of Consultation <span className="text-red-500">*</span>
                </h3>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="consultationMode"
                      value="In-person"
                      checked={consultationMode === 'In-person'}
                      onChange={(e) => setConsultationMode(e.target.value)}
                      className="mr-2"
                    />
                    <span className="text-sm">In-person</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="consultationMode"
                      value="Online Video Consultation"
                      checked={consultationMode === 'Online Video Consultation'}
                      onChange={(e) => setConsultationMode(e.target.value)}
                      className="mr-2"
                    />
                    <span className="text-sm">Online Video Consultation</span>
                  </label>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  For online consultation, you will receive a call link 15 minutes before the appointment.
                </p>
              </div>
            </div>

            {/* Payment Details */}
            <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
              <div className="flex items-center mb-4">
                <span className="text-blue-600 mr-2">💳</span>
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Payment Details</h2>
              </div>
              
              <div className="grid grid-cols-3 gap-4 mb-6">
                <button
                  onClick={() => setPaymentMethod('Card')}
                  className={`p-4 border rounded-lg text-center ${
                    paymentMethod === 'Card' 
                      ? 'border-blue-600 bg-blue-50' 
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <div className="text-2xl mb-2">💳</div>
                  <span className="text-sm">Card</span>
                </button>
                
                <button
                  onClick={() => setPaymentMethod('Cash')}
                  className={`p-4 border rounded-lg text-center ${
                    paymentMethod === 'Cash' 
                      ? 'border-blue-600 bg-blue-50' 
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <div className="text-2xl mb-2">💰</div>
                  <span className="text-sm">Cash</span>
                </button>
                
                <button
                  onClick={() => setPaymentMethod('UPI')}
                  className={`p-4 border rounded-lg text-center ${
                    paymentMethod === 'UPI' 
                      ? 'border-blue-600 bg-blue-50' 
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <div className="text-2xl mb-2">📱</div>
                  <span className="text-sm">UPI</span>
                </button>
              </div>
            </div>

            {/* Special Instructions */}
            <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Special Instructions (Optional)</h3>
              <textarea
                rows={3}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Any special requirements or information you want to share with the doctor"
              />
            </div>

            {/* Previous Medical History */}
            <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Previous Medical History</h3>
              <div className="grid sm:grid-cols-3 gap-4">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">Diabetes</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">Hypertension</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">Heart Disease</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">Other</span>
                </label>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
              <label className="flex items-start">
                <input type="checkbox" className="mr-2 mt-1" />
                <span className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                  I agree to the Terms and Conditions and Privacy Policy. I understand the Cancellation Policy and consent to the processing of my personal data for appointment purposes.
                </span>
              </label>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="flex-1 py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                Cancel
              </button>
              <button className="flex-1 py-3 px-6 bg-gray-400 text-white rounded-lg">
                Save as Draft
              </button>
              <button className="flex-1 py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                Confirm Booking
              </button>
            </div>
          </div>

          {/* Right Column - Booking Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 lg:sticky lg:top-8">
              <div className="flex items-center mb-4">
                <span className="text-blue-600 mr-2">📋</span>
                <h2 className="text-lg font-semibold text-gray-900">Booking Summary</h2>
              </div>
              
              <div className="space-y-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Doctor:</span>
                  <span className="font-medium">Dr. Ayush Sharma (Cardiologist)</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Date & Time:</span>
                  <span className="font-medium">Wed, 15 Jun 2023 | 10:30 AM</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Consultation Mode:</span>
                  <span className="font-medium">{consultationMode}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Patient:</span>
                  <span className="font-medium">John Doe</span>
                </div>
                
                <hr className="my-4" />
                
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total Amount:</span>
                  <span className="text-blue-600">₹800</span>
                </div>
              </div>
              
              <div className="mt-6 pt-4 border-t">
                <p className="text-xs text-gray-500 text-center mb-4">
                  Need help with your booking? Contact our support team
                </p>
                
                <div className="flex justify-center space-x-4 text-xs text-gray-500">
                  <span>🔒 Secure Payment</span>
                  <span>🛡️ Privacy Protected</span>
                  <span>📞 24/7 Support</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingAppointment;