import { Link } from 'react-router-dom';

const Home = () => {
  return (
    <div className="min-h-screen bg-white text-gray-800">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-6 py-20">
        <div className="max-w-6xl mx-auto flex flex-col md:flex-row items-center gap-10">
          <div className="md:flex-1 max-w-2xl">
            <h1 className="text-4xl md:text-5xl font-extrabold mb-6 leading-tight">
              Your Health, Our Priority —<br />Complete Healthcare Management
            </h1>
            <p className="text-lg md:text-xl text-blue-200 mb-10 leading-relaxed">
              Access world-class treatment and services from the comfort of your home.<br />
              Book appointments, find doctors, search medicines, and get the best treatment, all in one place.
            </p>
            <div className="flex flex-col sm:flex-row gap-5">
              <Link
                to="/bookappointment"
                className="inline-flex items-center justify-center gap-2 px-8 py-3 rounded-full bg-green-600 text-white font-semibold shadow-md hover:bg-green-700 transition"
              >
                📅 Book Appointment Now
              </Link>
              <Link
                to="/hospitalnearby"
                className="inline-flex items-center justify-center gap-2 px-8 py-3 rounded-full border-2 border-white bg-transparent text-white font-semibold hover:bg-white hover:text-blue-700 transition"
              >
                🔍 Find Doctors Near You
              </Link>
            </div>
            <div className="flex flex-wrap gap-8 mt-12 text-blue-200 font-medium">
              <div className="flex items-center gap-2">
                <span>🛡️</span> Certified Doctors
              </div>
              <div className="flex items-center gap-2">
                <span>🕐</span> 24/7 Support
              </div>
              <div className="flex items-center gap-2">
                <span>🏆</span> Video Consultation
              </div>
            </div>
          </div>

          <div className="md:flex-1 hidden md:block">
            {/* You can add an image or illustration here for better modern feel */}
            <img
              src="https://images.unsplash.com/photo-1588776814546-438f64d6b1e6?auto=format&fit=crop&w=600&q=80"
              alt="Doctor with patient"
              className="rounded-xl shadow-lg"
            />
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 px-6 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-blue-900 mb-4">Our Services</h2>
          <p className="text-blue-700 max-w-3xl mx-auto">
            Access our comprehensive healthcare services designed to provide you with the best medical care and support.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-10">
          {/* Book Appointment */}
          <div className="bg-white rounded-xl shadow-md border-l-8 border-green-500 p-8 hover:shadow-lg transition">
            <div className="w-14 h-14 bg-green-100 rounded-lg flex items-center justify-center mb-6 text-green-600 text-3xl">
              📅
            </div>
            <h3 className="text-xl font-semibold mb-3 text-blue-900">Book Appointment</h3>
            <p className="text-gray-700 mb-6">
              Schedule appointments with qualified doctors at your convenience. Choose from available time slots that work for you.
            </p>
            <button className="text-green-600 font-semibold hover:text-green-800 transition">
              Book Now →
            </button>
          </div>

          {/* Find Doctors */}
          <div className="bg-white rounded-xl shadow-md border-l-8 border-blue-600 p-8 hover:shadow-lg transition">
            <div className="w-14 h-14 bg-blue-100 rounded-lg flex items-center justify-center mb-6 text-blue-600 text-3xl">
              🔍
            </div>
            <h3 className="text-xl font-semibold mb-3 text-blue-900">Find Doctors</h3>
            <p className="text-gray-700 mb-6">
              Discover qualified healthcare professionals in your area. Filter by specialty, location, and availability.
            </p>
            <button className="text-blue-600 font-semibold hover:text-blue-800 transition">
              Search Doctors →
            </button>
          </div>

          {/* Medicine Search */}
          <div className="bg-white rounded-xl shadow-md border-l-8 border-yellow-600 p-8 hover:shadow-lg transition">
            <div className="w-14 h-14 bg-yellow-100 rounded-lg flex items-center justify-center mb-6 text-yellow-600 text-3xl">
              💊
            </div>
            <h3 className="text-xl font-semibold mb-3 text-blue-900">Medicine Search</h3>
            <p className="text-gray-700 mb-6">
              Search for medicines and check their availability at nearby pharmacies. Get detailed information and pricing.
            </p>
            <button className="text-yellow-600 font-semibold hover:text-yellow-800 transition">
              Search Medicine →
            </button>
          </div>

          {/* Emergency Services */}
          <div className="bg-white rounded-xl shadow-md border-l-8 border-brown-600 p-8 hover:shadow-lg transition">
            <div className="w-14 h-14 bg-amber-100 rounded-lg flex items-center justify-center mb-6 text-yellow-700 text-3xl">
              🚨
            </div>
            <h3 className="text-xl font-semibold mb-3 text-blue-900">Emergency Services</h3>
            <p className="text-gray-700 mb-6">
              24/7 emergency medical assistance. Quick access to emergency contacts and nearest hospitals.
            </p>
            <button className="text-amber-700 font-semibold hover:text-amber-900 transition">
              Emergency Help →
            </button>
          </div>
        </div>
      </section>

      {/* Top Rated Hospitals */}
      <section className="py-20 px-6 bg-blue-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-blue-900 mb-4">Top Rated Hospitals</h2>
            <p className="text-blue-700 max-w-3xl mx-auto">
              Our network includes some of the most prestigious and highly rated healthcare institutions.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                name: "City General Hospital",
                specialty: "Multi-specialty",
                rating: 4.8,
                reviews: "2.1k reviews",
                colorBorder: "border-green-500",
              },
              {
                name: "Heart Care Center",
                specialty: "Cardiology",
                rating: 4.9,
                reviews: "1.8k reviews",
                colorBorder: "border-blue-600",
              },
              {
                name: "Children's Medical Center",
                specialty: "Pediatrics",
                rating: 4.7,
                reviews: "1.5k reviews",
                colorBorder: "border-amber-600",
              },
              {
                name: "Wellness Hospital",
                specialty: "General Medicine",
                rating: 4.6,
                reviews: "2.3k reviews",
                colorBorder: "border-brown-500",
              },
            ].map(({ name, specialty, rating, reviews, colorBorder }, index) => (
              <div
                key={index}
                className={`bg-white rounded-xl shadow-md p-6 border-l-8 ${colorBorder} hover:shadow-lg transition`}
              >
                <div className="w-16 h-16 bg-gray-200 rounded-full mb-6 flex items-center justify-center text-3xl">
                  🏥
                </div>
                <h3 className="text-xl font-semibold mb-1 text-blue-900">{name}</h3>
                <p className="text-blue-700 text-sm mb-4">{specialty}</p>
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className={i < Math.floor(rating) ? '' : 'text-gray-300'}>
                        ⭐
                      </span>
                    ))}
                  </div>
                  <span className="ml-2 text-sm text-blue-700 font-semibold">{rating}</span>
                </div>
                <p className="text-xs text-blue-500 mb-6">{reviews}</p>
                <button className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition">
                  <Link to="/">View Hospital</Link>
                </button>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <button className="bg-blue-700 text-white px-10 py-3 rounded-full hover:bg-blue-800 transition">
              View All Hospitals
            </button>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 px-6 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-blue-900 mb-4">Why Choose Us</h2>
          <p className="text-blue-700 max-w-3xl mx-auto">
            We are committed to providing the highest quality healthcare with a focus on patient satisfaction.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-16">
          {/* Certifications & Accreditations */}
          <div>
            <h3 className="text-2xl font-semibold mb-10 text-blue-900">Certifications & Accreditations</h3>
            <div className="space-y-6 text-blue-700">
              {[
                { icon: '🛡️', bg: 'bg-green-100', title: 'ISO Certified', desc: 'Quality management standards', colorText: 'text-green-600' },
                { icon: '🏆', bg: 'bg-blue-100', title: 'HIPAA Compliant', desc: 'Patient privacy protection', colorText: 'text-blue-600' },
                { icon: '⭐', bg: 'bg-amber-100', title: 'JCI Accredited', desc: 'International healthcare standards', colorText: 'text-yellow-600' },
                { icon: '🔰', bg: 'bg-brown-100', title: 'AHA Recognized', desc: 'American Hospital Association', colorText: 'text-brown-600' },
              ].map(({ icon, bg, title, desc, colorText }, i) => (
                <div key={i} className="flex items-center gap-5">
                  <div className={`w-14 h-14 rounded-lg flex items-center justify-center ${bg} text-3xl font-bold ${colorText}`}>
                    {icon}
                  </div>
                  <div>
                    <h4 className="font-semibold text-blue-900 text-lg">{title}</h4>
                    <p className="text-blue-700 text-sm">{desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Patient Testimonials */}
          <div>
            <h3 className="text-2xl font-semibold mb-10 text-blue-900">Patient Testimonials</h3>
            <div className="space-y-10">
              {[
                {
                  name: 'Jennifer L.',
                  rating: 5,
                  text: 'The online booking system made it so easy to schedule my appointment. The entire staff at City General Hospital was professional and caring.',
                },
                {
                  name: 'Robert M.',
                  rating: 5,
                  text: "Thank god I'm alive medicine availability feature going on my advanced stage of cancer was fast. Got medicine within 24 hrs and helped me a lot.",
                },
              ].map(({ name, rating, text }, i) => (
                <div key={i} className="bg-white p-7 rounded-xl shadow-md">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-blue-200 rounded-full flex items-center justify-center text-2xl">
                      👤
                    </div>
                    <div className="ml-4">
                      <h4 className="font-semibold text-blue-900 text-lg">{name}</h4>
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <span key={i}>⭐</span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <p className="text-blue-700 text-sm leading-relaxed">{text}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-10 mt-20 pt-16 border-t border-blue-200 text-blue-700">
          {[
            { num: '50,000+', label: 'Patients Served' },
            { num: '1,000+', label: 'Expert Doctors' },
            { num: '98%', label: 'Satisfaction Rate' },
            { num: '24/7', label: 'Support Available' },
          ].map(({ num, label }, i) => (
            <div key={i} className="text-center font-semibold">
              <div className="text-4xl text-blue-700 mb-1">{num}</div>
              <div className="text-lg">{label}</div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default Home;