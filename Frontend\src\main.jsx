import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import {
  createBrowserRouter,
  RouterProvider,
} from "react-router-dom";
import Login from './pages/Login.jsx';
import FindHospitals from './pages/FindHospitals.jsx';
import BookAppointment from './pages/BookAppointment.jsx';
import BookingAppointment from './pages/BookingAppointment.jsx';
import Home from './pages/Home.jsx';

const router = createBrowserRouter([
  {
    path: "/",
    element: <App/>,
    children:[
      {
        path:"/",
        element:<Home/>
      },
      {
        path:"/login",
        element:<Login/>
      },
      {
        path:'/hospitalnearby',
        element:<FindHospitals/>
      },
      {
        path:'/bookappointment',
        element:<BookAppointment/>
      },
      {
        path:'/apointment',
        element:<BookingAppointment/>
      },
      {
        path:'/hospitalnearby',
        element:<FindHospitals/>
      }
    ]
  },
]);

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <RouterProvider router={router}/>
  </StrictMode>,
)
