import React from 'react';

const Navbar = () => {
  return (
    <nav className="bg-white shadow-sm px-4 py-3">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">M</span>
          </div>
          <span className="text-xl font-bold text-gray-800">MediCare</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <a href="#" className="text-gray-700 hover:text-blue-600">Find</a>
          <a href="#" className="text-gray-700 hover:text-blue-600">Book Appointment</a>
          <a href="#" className="text-gray-700 hover:text-blue-600">Medicines</a>
          <a href="#" className="text-gray-700 hover:text-blue-600">About</a>
          <a href="#" className="text-gray-700 hover:text-blue-600">Contact</a>
        </div>
        
        <div className="flex items-center space-x-4">
          <span className="text-red-500 text-sm hidden md:inline">Emergency</span>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700">
            Sign In
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;