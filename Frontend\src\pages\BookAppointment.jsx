import React, { useState } from 'react';

const BookAppointment = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialty, setSelectedSpecialty] = useState('All Specialties');
  const [selectedAvailability, setSelectedAvailability] = useState('');
  const [selectedDate, setSelectedDate] = useState('');

  const doctors = {
    Cardiology: [
      {
        id: 1,
        name: "Dr. <PERSON>",
        credentials: "MD, FACC",
        specialty: "Interventional Cardiology",
        rating: 4.8,
        reviews: 234,
        availability: "Available Now",
        waitingPatients: 2,
        experience: "15+ years experience",
        timeSlots: ["9:00 AM", "11:30 AM", "2:00 PM", "3:30 PM", "5:00 PM", "6:30 PM"],
        image: "👨‍⚕️"
      },
      {
        id: 2,
        name: "Dr. <PERSON>",
        credentials: "MD, PHD",
        specialty: "Cardiac Electrophysiology",
        rating: 4.7,
        reviews: 198,
        availability: "Available Later Today",
        waitingPatients: 5,
        experience: "12+ years experience",
        timeSlots: ["9:00 AM", "11:00 AM", "2:00 PM", "3:30 PM", "5:00 PM", "6:30 PM"],
        image: "👨‍⚕️"
      },
      {
        id: 3,
        name: "Dr. Robert <PERSON>",
        credentials: "MD, FACC, FSCAI",
        specialty: "Non-Invasive Cardiology",
        rating: 4.9,
        reviews: 156,
        availability: "Not Available Today",
        waitingPatients: 0,
        experience: "20+ years experience",
        timeSlots: ["9:00 AM", "11:30 AM", "2:00 PM", "3:30 PM", "5:00 PM", "6:30 PM"],
        image: "👨‍⚕️"
      }
    ],
    Neurology: [
      {
        id: 4,
        name: "Dr. Emily Rodriguez",
        credentials: "MD, PhD, FAAN",
        specialty: "Clinical Neurology",
        rating: 4.6,
        reviews: 187,
        availability: "Available Now",
        waitingPatients: 3,
        experience: "18+ years experience",
        timeSlots: ["10:00 AM", "12:30 PM", "2:30 PM", "4:00 PM", "5:30 PM", "7:00 PM"],
        image: "👩‍⚕️"
      },
      {
        id: 5,
        name: "Dr. James Wilson",
        credentials: "MD, FAANS",
        specialty: "Neurological Surgery",
        rating: 4.5,
        reviews: 142,
        availability: "Available Later Today",
        waitingPatients: 7,
        experience: "16+ years experience",
        timeSlots: ["9:30 AM", "11:00 AM", "1:30 PM", "4:00 PM", "5:30 PM", "7:00 PM"],
        image: "👨‍⚕️"
      },
      {
        id: 6,
        name: "Dr. Sophia Kim",
        credentials: "MD, PhD",
        specialty: "Pediatric Neurology",
        rating: 4.8,
        reviews: 112,
        availability: "Available Now",
        waitingPatients: 1,
        experience: "14+ years experience",
        timeSlots: ["9:00 AM", "10:30 AM", "1:00 PM", "2:30 PM", "4:00 PM", "5:30 PM"],
        image: "👩‍⚕️"
      }
    ],
    "General Medicine": [
      {
        id: 7,
        name: "Dr. David Thompson",
        credentials: "MD, FAAFP",
        specialty: "Internal Medicine",
        rating: 4.4,
        reviews: 178,
        availability: "Available Now",
        waitingPatients: 4,
        experience: "8+ years experience",
        timeSlots: ["9:30 AM", "11:00 AM", "12:30 PM", "2:00 PM", "3:30 PM", "5:00 PM"],
        image: "👨‍⚕️"
      },
      {
        id: 8,
        name: "Dr. Lisa Martinez",
        credentials: "MD, MPH",
        specialty: "Family Medicine",
        rating: 4.6,
        reviews: 201,
        availability: "Available Later Today",
        waitingPatients: 6,
        experience: "12+ years experience",
        timeSlots: ["10:00 AM", "11:30 AM", "1:00 PM", "4:00 PM", "5:30 PM"],
        image: "👩‍⚕️"
      },
      {
        id: 9,
        name: "Dr. John Anderson",
        credentials: "DO, FAAFP",
        specialty: "Preventive Medicine",
        rating: 4.7,
        reviews: 167,
        availability: "Not Available Today",
        waitingPatients: 0,
        experience: "9+ years experience",
        timeSlots: ["9:00 AM", "10:30 AM", "12:00 PM", "1:30 PM", "3:00 PM", "4:30 PM"],
        image: "👨‍⚕️"
      }
    ]
  };

  const getAvailabilityColor = (availability) => {
    switch (availability) {
      case 'Available Now':
        return 'text-green-600';
      case 'Available Later Today':
        return 'text-yellow-600';
      case 'Not Available Today':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getAvailabilityDot = (availability) => {
    switch (availability) {
      case 'Available Now':
        return 'bg-green-500';
      case 'Available Later Today':
        return 'bg-yellow-500';
      case 'Not Available Today':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const renderStars = (rating) => {
    return [...Array(5)].map((_, i) => (
      <span 
        key={i} 
        className={`${i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'}`}
      >
        ⭐
      </span>
    ));
  };

  const getTotalDoctors = () => {
    return Object.values(doctors).reduce((total, specialty) => total + specialty.length, 0);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white px-4 py-3 border-b">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center text-sm text-gray-600">
            <span>Home</span>
            <span className="mx-2">›</span>
            <span>Find Hospitals</span>
            <span className="mx-2">›</span>
            <span className="text-blue-600">Book Appointment</span>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Book Doctor Appointment</h1>
            <p className="text-gray-600">City General Hospital, Downtown Medical Center</p>
          </div>
          
          {/* Quick Actions Sidebar */}
          <div className="bg-white rounded-lg shadow-sm p-6 w-64">
            <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button className="w-full flex items-center p-3 text-left hover:bg-red-50 rounded-lg">
                <span className="text-red-500 mr-3">🚨</span>
                <span className="text-red-600 font-medium">Emergency Booking</span>
              </button>
              <button className="w-full flex items-center p-3 text-left hover:bg-blue-50 rounded-lg">
                <span className="text-blue-500 mr-3">💻</span>
                <span className="text-blue-600">Video Consultation</span>
              </button>
              <button className="w-full flex items-center p-3 text-left hover:bg-purple-50 rounded-lg">
                <span className="text-purple-500 mr-3">📅</span>
                <span className="text-purple-600">Same-day Appointments</span>
              </button>
              <button className="w-full flex items-center p-3 text-left hover:bg-green-50 rounded-lg">
                <span className="text-green-500 mr-3">💊</span>
                <span className="text-green-600">Nearby Pharmacies</span>
              </button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="grid md:grid-cols-4 gap-4">
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
              <input
                type="text"
                placeholder="Search doctor by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <select 
              value={selectedSpecialty}
              onChange={(e) => setSelectedSpecialty(e.target.value)}
              className="p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="All Specialties">All Specialties</option>
              <option value="Cardiology">Cardiology</option>
              <option value="Neurology">Neurology</option>
              <option value="General Medicine">General Medicine</option>
            </select>

            <select 
              value={selectedAvailability}
              onChange={(e) => setSelectedAvailability(e.target.value)}
              className="p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Availability</option>
              <option value="Available Now">Available Now</option>
              <option value="Available Later Today">Available Later Today</option>
              <option value="Not Available Today">Not Available Today</option>
            </select>

            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Results Count */}
        <p className="text-gray-600 mb-8">{getTotalDoctors()} doctors found</p>

        {/* Doctor Sections by Specialty */}
        {Object.entries(doctors).map(([specialty, doctorsList]) => (
          <div key={specialty} className="mb-12">
            {/* Specialty Header */}
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-4">
                <span className="text-white font-semibold text-sm">
                  {specialty === 'Cardiology' ? '❤️' : 
                   specialty === 'Neurology' ? '🧠' : '🏥'}
                </span>
              </div>
              <h2 className="text-2xl font-bold text-gray-900">{specialty}</h2>
            </div>

            {/* Doctor Cards */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {doctorsList.map((doctor) => (
                <div key={doctor.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
                  {/* Doctor Profile */}
                  <div className="flex items-start mb-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mr-4">
                      <span className="text-2xl">{doctor.image}</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900">{doctor.name}</h3>
                      <p className="text-sm text-gray-600 mb-1">{doctor.credentials}</p>
                      <p className="text-sm text-blue-600 mb-2">{doctor.specialty}</p>
                      
                      {/* Rating */}
                      <div className="flex items-center mb-2">
                        <div className="flex items-center">
                          {renderStars(doctor.rating)}
                        </div>
                        <span className="ml-2 text-sm text-gray-600">
                          {doctor.rating} ({doctor.reviews} reviews)
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Availability Status */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <div className={`w-2 h-2 rounded-full mr-2 ${getAvailabilityDot(doctor.availability)}`}></div>
                      <span className={`text-sm font-medium ${getAvailabilityColor(doctor.availability)}`}>
                        {doctor.availability}
                      </span>
                    </div>
                    {doctor.waitingPatients > 0 && (
                      <span className="text-sm text-gray-600">
                        {doctor.waitingPatients} patients waiting
                      </span>
                    )}
                  </div>

                  {/* Experience */}
                  <p className="text-sm text-gray-600 mb-4">📅 {doctor.experience}</p>

                  {/* Available Time Slots */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">Available Time Slots</h4>
                    <div className="grid grid-cols-3 gap-2">
                      {doctor.timeSlots.map((slot, index) => (
                        <button 
                          key={index}
                          className={`p-2 text-xs border rounded-lg text-center ${
                            doctor.availability === 'Available Now' ? 
                            'border-blue-300 text-blue-600 hover:bg-blue-50' :
                            doctor.availability === 'Available Later Today' ?
                            'border-yellow-300 text-yellow-600 hover:bg-yellow-50' :
                            'border-gray-300 text-gray-400 cursor-not-allowed'
                          }`}
                          disabled={doctor.availability === 'Not Available Today'}
                        >
                          {slot}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <button 
                      className={`flex-1 py-2 rounded-lg text-sm font-medium ${
                        doctor.availability === 'Not Available Today' ?
                        'bg-gray-300 text-gray-500 cursor-not-allowed' :
                        'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                      disabled={doctor.availability === 'Not Available Today'}
                    >
                      Book Slot
                    </button>
                    <button className="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 text-sm">
                      View Profile
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BookAppointment;